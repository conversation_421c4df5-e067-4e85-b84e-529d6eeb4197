import re
import sys
import traceback
from datetime import datetime, timezone
import scrapy
from bs4 import BeautifulSoup
from scrapy.exceptions import CloseSpider
from scrapy_playwright.page import PageMethod

from jobsitescraper.log_manager import CustomLogger
from jobsitescraper.utils import env


class Adecco_CrawlingManager(scrapy.Spider):
    name = "adecco.ch"
    close_down = False
    page_num = 2
    config = {}
    count = 0
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "adecco.ch"
        config["BaseUrl"] = "https://www.adecco.ch/"
        config["StartUrl"] = "https://www.adecco.com/en-ch/job-search?pg=2"
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config["CompanyName"] = "Adecco"
        config["MaxPagesToCrawl"] = 10
        config["MaxJobsToCrawl"] = 500
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config["RecentJobs"] = True
        config['DeleteAllJobsOnStart'] = False
        return config

    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Crawler Started")
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(self.config["SourceKey"], "Recent Data thread started")
                    yield scrapy.Request(
                        url=self.config["StartUrl"],
                        method='GET',
                        callback=self.parse_all,
                        meta={
                            "playwright": True,
                            "playwright_context": "new",
                            "playwright_page_methods": [
                                PageMethod("evaluate", "window.scrollBy(0, document.body.scrollHeight)"),
                                PageMethod("wait_for_timeout", 17000),
                                PageMethod("wait_for_selector", ".mb0"),
                            ],
                        }
                    )
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_all(self, response):
        try:
            for i in response.xpath('//section[@class="scrollbar-box align-items_center_tablet full-width_tablet"]/article'):
                ids = i.xpath('//div/div[1]/@id').get()
                print(ids)

        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_job(self, response):
        if self.close_down:
            raise CloseSpider("Took down by Analyzer")
        try:
            ad = {}

            jobTitle = response.xpath('//div[@class="job--task-specifics"]/h2/text()').get()
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            city = response.xpath('//span[@id="lblCity"]/text()').get()
            loc = response.xpath('//span[@id="lblState"]/text()').get()
            jobLocation = city + loc
            ad['JobLocation'] = jobLocation

            category = response.xpath(
                '//span[text()="Category                                "]/following-sibling::div/span/text()').get()
            if category is not None:
                category = category.strip()
            else:
                category = ''

            JobType = response.xpath(
                '//span[text()="Contract Type                                "]/following-sibling::div/span/text()').get()
            if JobType is not None:
                JobType = JobType.strip()
            else:
                JobType = ''

            Reference = response.xpath(
                '//span[text()="External Reference                                "]/following-sibling::div/span/text()').get()
            if Reference is not None:
                Reference = Reference.strip()
            else:
                Reference = ''

            education = response.xpath(
                '//span[text()="Educational Requirements                                "]/following-sibling::div/span/text()').get()
            if education is not None:
                education = education.strip()
            else:
                education = ''

            website_text = response.body.decode("utf-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
            description = jobs_soup.find('div', {"class": "job--description box-padding"})
            if description is not None:
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''

            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = jobLocation
            ad['EducationalRequirements'] = education
            ad['Category'] = category
            ad['JobType'] = JobType
            ad['Reference'] = Reference
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['SourceUID'] = response.url

            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]',
                                   cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone

            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(
                self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()
